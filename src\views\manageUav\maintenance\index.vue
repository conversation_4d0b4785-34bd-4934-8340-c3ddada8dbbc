<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="维保设备" prop="deviceName">
        <el-select v-model="queryParams.deviceName" placeholder="请选择维保设备" clearable>
          <el-option v-for="dict in dict.type.maintenance_device" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="维保时间" prop="tm">
        <el-date-picker clearable v-model="queryParams.tm" type="date" value-format="yyyy-MM-dd" placeholder="请选择维保时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="维保状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择维保状态" clearable>
          <el-option v-for="dict in dict.type.maintenance_status" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['system:maintenance:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:maintenance:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:maintenance:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['system:maintenance:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <div v-tableHeight>
      <el-table v-loading="loading" height="100%" :data="maintenanceList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="维保任务名称" align="center" prop="taskName">
          <template slot-scope="scope">
            <el-link type="primary" :underline="false" @click="handleTaskName(scope.row)">
              {{ scope.row.taskName }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="维保设备" align="center" prop="deviceName">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.maintenance_device" :value="scope.row.deviceName" />
          </template>
        </el-table-column>
        <el-table-column label="执行人" align="center" prop="personName" />
        <el-table-column label="维保时间" align="center" prop="tm" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.tm, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="维保状态" align="center" prop="status">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.maintenance_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="维保任务描述" align="center" prop="desc" />
        <el-table-column label="图片" align="center" prop="imgUrl">
          <template slot-scope="scope">
            <el-image v-if="scope.row.imgUrl" :src="getImage(scope.row.imgUrl)" style=" height: 100px;"
              :preview-src-list="getImageList(scope.row.imgUrl)" />
          </template>
        </el-table-column>
        <!--      <el-table-column label="文件" align="center" prop="fileUrl" />-->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['system:maintenance:edit']">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
              v-hasPermi="['system:maintenance:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改维保记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="110px" :disabled="detail">
        <el-row>
          <el-col :span="12">
            <el-form-item label="维保任务名称" prop="taskName">
              <el-input v-model="form.taskName" placeholder="请输入维保任务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维保设备" prop="deviceName">
              <el-select v-model="form.deviceName" placeholder="请选择维保设备" clearable style="width: 100%;">
                <el-option v-for="dict in dict.type.maintenance_device" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="执行人" prop="personName">
              <el-input v-model="form.personName" placeholder="请输入执行人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维保时间" prop="tm">
              <el-date-picker clearable v-model="form.tm" style="width: 100%;" type="date" value-format="yyyy-MM-dd"
                placeholder="请选择维保时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维保状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择维保状态" style="width: 100%;">
                <el-option v-for="dict in dict.type.maintenance_status" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维保任务描述" prop="desc">
              <el-input v-model="form.desc" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="图片" prop="imgUrl">
              <image-upload v-model="form.imgUrl" :limit="3" :disabled="detail" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="文件" prop="fileUrl">
              <file-upload v-model="form.fileUrl" :fileSize="50" :limit="5" :disabled="detail" />
            </el-form-item>
          </el-col>


        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="!detail" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMaintenance, getMaintenance, delMaintenance, addMaintenance, updateMaintenance } from "@/api/uav/maintenance.js";

export default {
  name: "Maintenance",
  dicts: ['maintenance_status', 'maintenance_device'],
  data () {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 维保记录表格数据
      maintenanceList: [],
      // 弹出层标题
      title: "",
      detail: false,
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: null,
        deviceName: null,
        personName: null,
        tm: null,
        status: null,
        desc: null,
        imgUrl: null,
        fileUrl: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        taskName: [
          { required: true, message: "请输入维保任务名称", trigger: "blur" }
        ],
        deviceName: [
          { required: true, message: "请选择维保设备", trigger: "change" }
        ],
        personName: [
          { required: true, message: "请输入执行人", trigger: "blur" }
        ],
        tm: [
          { required: true, message: "请选择维保时间", trigger: "change" }
        ],
        status: [
          { required: true, message: "请选择维保状态", trigger: "change" }
        ],
        desc: [
          { required: true, message: "请输入维保任务描述", trigger: "blur" }
        ],
        imgUrl: [
          { required: false, message: "请上传图片", trigger: "change" }
        ],
        fileUrl: [
          { required: false, message: "请上传文件", trigger: "change" }
        ]
      },
      prefix: process.env.VUE_APP_BASE_API
    };
  },
  created () {
    this.getList();
  },
  methods: {
    handleTaskName (row) {
      getMaintenance(row.id).then(response => {
        this.form = response.data;
        this.open = true;
        this.detail = true;
        this.title = "维保任务详情";
      });

    },
    getImage (url) {
      if (url) {
        const urlList = url.split(",");
        return this.prefix + urlList[0];
      }
      return "";
    },
    getImageList (url) {
      if (url) {
        const urlList = url.split(",");
        return urlList.map(item => this.prefix + item);
      }
    },
    /** 查询维保记录列表 */
    getList () {
      this.loading = true;
      listMaintenance(this.queryParams).then(response => {
        this.maintenanceList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    // 取消按钮
    cancel () {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset () {
      this.form = {
        id: null,
        taskName: null,
        deviceName: null,
        personName: null,
        tm: null,
        status: null,
        desc: null,
        imgUrl: null,
        fileUrl: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd () {
      this.reset();
      this.detail = false;
      this.open = true;
      this.title = "添加维保记录";
    },
    /** 修改按钮操作 */
    handleUpdate (row) {
      this.reset();
      const id = row.id || this.ids
      this.detail = false;
      getMaintenance(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改维保记录";
      });
    },
    /** 提交按钮 */
    submitForm () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateMaintenance(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMaintenance(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete (row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除维保记录编号为"' + ids + '"的数据项？').then(function () {
        return delMaintenance(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport () {
      this.download('uav/maintenance/export', {
        ...this.queryParams
      }, ` 维保记录_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style scoped lang="scss">
.dialog-footer {
  text-align: center;
}
</style>
